{"editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "explicit", "source.sortMembers": "explicit"}, "css.validate": false, "less.validate": false, "scss.validate": false, "files.associations": {"*.css": "tailwindcss"}, "editor.quickSuggestions": {"strings": true}, "tailwindCSS.includeLanguages": {"typescript": "typescript", "javascript": "javascript", "typescriptreact": "typescriptreact", "javascriptreact": "javascriptreact"}, "tailwindCSS.experimental.classRegex": [["className\\s*=\\s*[\"'`]([^\"'`]*)[\"'`]", "([^\"'`]*)"], ["class\\s*=\\s*[\"'`]([^\"'`]*)[\"'`]", "([^\"'`]*)"]]}