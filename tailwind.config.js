/** @type {import('tailwindcss').Config} */
module.exports = {
  // NOTE: Update this to include the paths to all files that contain Nativewind classes.
  content: ["./app/**/*.{js,jsx,ts,tsx}", "./components/**/*.{js,jsx,ts,tsx}"],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      colors: {
        light: {
          background: '#ffffff',
          textColor: '#000000',
          primary: '#008cffff',
          secondary: '#00f8ffff',
        },
        dark: {
          background: '#000000',
          textColor: '#ffffff',
          primary: '#008cffff',
          secondary: '#00f8ffff',
        },
      },
    },
  },
  plugins: [],
}